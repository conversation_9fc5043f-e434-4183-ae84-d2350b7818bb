import React from "react";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

import { useSession } from "@/modules/login/auth-provider";
import BiometricSettings from "@/components/screens/settings/biometric-settings";
import { ToastTest } from "@/components/test/toast-test";
import { ReservationErrorTest } from "@/components/test/reservation-error-test";
import { AppointmentTest } from "@/components/test/appointment-test";

import {
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from "@/components/ui/toast";
import { Button, ButtonText } from "@/components/ui/button";
import { OverlayProvider } from "@gluestack-ui/overlay";

function Example() {
  const toast = useToast();
  const [toastId, setToastId] = React.useState("0");
  const handleToast = () => {
    if (!toast.isActive(toastId)) {
      showNewToast();
    }
  };
  const showNewToast = () => {
    const newId = Math.random().toString();
    setToastId(newId);
    toast.show({
      id: newId,
      placement: "top",
      duration: 3000,
      render: ({ id }) => {
        const uniqueToastId = "toast-" + id;
        return (
          <Toast
            nativeID={uniqueToastId}
            action="muted"
            variant="solid"
            className="flex-1"
          >
            <ToastTitle>Hello!</ToastTitle>
            <ToastDescription>
              This is a customized toast message.
            </ToastDescription>
          </Toast>
        );
      },
    });
  };
  return (
    <Button onPress={handleToast}>
      <ButtonText>Press Me</ButtonText>
    </Button>
  );
}

const Settings = () => {
  const { signOut } = useSession();

  return (
    <VStack space="md" className="bg-background-0 mt-12">
      {/* Biometric Settings */}
      <BiometricSettings />

      <VStack className="px-4" space="md">
        <OverlayProvider>
          {/* Toast Test Components */}
          <ToastTest />
          {/* <ReservationErrorTest /> */}
          {/* <AppointmentTest /> */}

          <Text className="font-semibold">Logout</Text>
          <VStack className="px-4" space="md">
            <Button
              onPress={signOut}
              variant="solid"
              className="w-full bg-[#00697B] rounded-full"
            >
              <Text className="text-white font-dm-sans-medium text-center">
                Logout
              </Text>
            </Button>
          </VStack>
          <Example />
        </OverlayProvider>
      </VStack>
    </VStack>
  );
};

export default Settings;
